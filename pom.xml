<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>fm.lizhi.ocean</groupId>
    <artifactId>lz-app-ocean-seal</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>lz-app-ocean-seal</name>
    <description>lz-app-ocean-seal</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <lz-common-dependencies-bom.version>1.4.28</lz-common-dependencies-bom.version>
        <lz-commons-connector-dc-excep-interim.version>2.0.3</lz-commons-connector-dc-excep-interim.version>
        <lz-app-commons-util.version>2.7.3</lz-app-commons-util.version>
        <lz-app-client-protobuf.version>4.6.14</lz-app-client-protobuf.version>
        <demo.version>1.0-SNAPSHOT</demo.version>
        <lz-datacenter-user.version>2.0.3-SNAPSHOT</lz-datacenter-user.version>
        <junit.version>4.8</junit.version>
        <logback-core.version>1.1.2</logback-core.version>
        <logback-classic.version>1.1.2</logback-classic.version>
        <logback-access.version>1.1.2</logback-access.version>
        <lz-app-business-util.version>6.2.8</lz-app-business-util.version>
        <lz-app-sdk.version>1.2.0-SNAPSHOT</lz-app-sdk.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-connector-dc-excep-interim</artifactId>
            <version>${lz-commons-connector-dc-excep-interim.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-connector-async</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-app-commons-util</artifactId>
            <version>${lz-app-commons-util.version}</version>
        </dependency>
		<dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
		</dependency><dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-util</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-config</artifactId>
        </dependency>
        <dependency>
	      <groupId>fm.lizhi.commons</groupId>
	      <artifactId>initiator</artifactId>
	    </dependency>
        <dependency>
            <groupId>fm.lizhi.app</groupId>
            <artifactId>lz-app-client-protobuf</artifactId>
            <version>${lz-app-client-protobuf.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-app-commons-conn</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.datacenter</groupId>
            <artifactId>lz-datacenter-user</artifactId>
            <version>${lz-datacenter-user.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <!--日志配置-->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback-core.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback-classic.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-access</artifactId>
            <version>${logback-access.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-app-business-util</artifactId>
            <version>${lz-app-business-util.version}</version>
        </dependency>
        <dependency>
		  <groupId>fm.lizhi</groupId>
		  <artifactId>lz-app-sdk</artifactId>
		  <version>${lz-app-sdk.version}</version>
		</dependency>
		<dependency>
	    	<groupId>com.dianping.cat</groupId>
	    	<artifactId>cat-client</artifactId>
	    </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
        </dependency>
        <dependency>
            <groupId>com.lizhi.idl.seal</groupId>
            <artifactId>idl-seal</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean</groupId>
            <artifactId>lz-ocean-seal-api</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>

            <groupId>fm.lizhi</groupId>
            <artifactId>lz-account-security-api</artifactId>
            <version>1.6.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yibasan.lizhi.tracker</groupId>
            <artifactId>lz-tracker-java</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-all</artifactId>
        </dependency>
    </dependencies>
    
    <dependencyManagement>
    	<dependencies>
      		<dependency>
			      <groupId>fm.lizhi.common</groupId>
			      <artifactId>lz-common-dependencies-bom</artifactId>
			      <version>${lz-common-dependencies-bom.version}</version>
			      <type>pom</type>
			      <scope>import</scope>
			  </dependency>
    	</dependencies>
  </dependencyManagement>
    
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>fm.lizhi.commons</groupId>
                    <artifactId>autoapi-maven-plugin</artifactId>
                    <version>0.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Central</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Plugin Repository</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <layout>default</layout>
            <snapshots>
            </snapshots>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
