1. 配置中心地址(测试环境)

http://configportalinoffice.lizhi.fm/config.html?#/appid=lz_app_ocean_seal
    
2. 本地开发启动类需要增加的JVM参数（灯塔和发布平台不需要）:

`-Dconf.env=office -Dconf.key=lz_app_ocean_seal -Dapp.name=lz_app_ocean_seal -Dregion=cn -DbusinessEnv=lizhi -DCAT_HOME=/tmp -Dmetadata.region=cn -Dmetadata.deploy.env=office -Dmetadata.business.env=lizhi -Dmetadata.service.name=lz_app_ocean_seal`


3. 需要获取环境参数请使用以下方法获取:

- 获取运行时的区域变量： `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getRegion()`
- 获取运行时的业务环境变量： `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getBusinessEnv()`
- 获取运行时的部署环境变量： `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getEnv()`
- 获取运行时的服务名称变量：  `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getServiceName()`